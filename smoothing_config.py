"""
Smoothing Configuration for Motor Performance Plotting

File: smoothing_config.py
Purpose: Configuration parameters for data smoothing and visualization
Safety Level: Non-Critical
Author: <PERSON>
Date: 2025-07-08
"""

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

"""
To adjust smoothing levels:

1. For LIGHT smoothing (preserve more detail):
   - Reduce SMOOTHING_WINDOW_SIZE (e.g., 25)
   - Reduce DECIMATION_FACTOR (e.g., 3)
   - Use 'light' preset

2. For HEAVY smoothing (remove more noise):
   - Increase SMOOTHING_WINDOW_SIZE (e.g., 100)
   - Increase DECIMATION_FACTOR (e.g., 10)
   - Use 'heavy' preset

3. For custom smoothing:
   - Modify individual parameters above
   - Test with your specific data

4. To disable smoothing entirely:
   - Set enable_smoothing=False in main script

Common adjustments:
- Noisy current measurements: Increase SMOOTHING_WINDOW_SIZE
- Too much detail lost: Decrease SMOOTHING_WINDOW_SIZE
- Plot too dense: Increase DECIMATION_FACTOR
- Plot too sparse: Decrease DECIMATION_FACTOR
"""

# =============================================================================
# SMOOTHING PARAMETERS
# =============================================================================

# Moving Average Smoothing
# Larger values = more smoothing but less detail
SMOOTHING_WINDOW_SIZE = 100

# Data Decimation (Point Reduction)
# Higher values = fewer points, cleaner plots
# 1 = no decimation, 5 = every 5th point, 10 = every 10th point
DECIMATION_FACTOR = 5

# Savitzky-Golay Filter Parameters
# Window length must be odd and larger than the polynomial order
# Larger window = more smoothing, higher order = better peak preservation
SAVGOL_WINDOW_LENGTH = 51  # Must be odd number
SAVGOL_POLY_ORDER = 3  # Usually 2-4 works well

# Outlier Removal
# Number of standard deviations for outlier detection
# Lower values = more aggressive outlier removal
OUTLIER_THRESHOLD = 3.0

# Minimum data points required after filtering
MIN_DATA_POINTS = 100

# =============================================================================
# PLOT STYLING PARAMETERS
# =============================================================================

# Figure size (width, height in inches)
FIGURE_SIZE = (14, 10)
COMPARISON_FIGURE_SIZE = (20, 8)  # Wider for side-by-side comparison

# DPI for saved images (higher = better quality, larger file)
SAVE_DPI = 300

# Line and marker styling
SMOOTHED_LINEWIDTH = 2.5
SMOOTHED_MARKERSIZE = 6
SMOOTHED_ALPHA = 0.8

RAW_LINEWIDTH = 1
RAW_MARKERSIZE = 2
RAW_ALPHA = 0.6

# Grid styling
GRID_ALPHA = 0.4
GRID_LINESTYLE = '--'
GRID_LINEWIDTH = 0.5

# =============================================================================
# ADVANCED SMOOTHING PRESETS
# =============================================================================

# Preset configurations for different noise levels
SMOOTHING_PRESETS = {
	'light': {
		'window_size': 25,
		'decimation': 3,
		'savgol_window': 25,
		'savgol_order': 2,
		'outlier_threshold': 2.5
	},
	'medium': {
		'window_size': 50,
		'decimation': 5,
		'savgol_window': 51,
		'savgol_order': 3,
		'outlier_threshold': 3.0
	},
	'heavy': {
		'window_size': 100,
		'decimation': 10,
		'savgol_window': 101,
		'savgol_order': 3,
		'outlier_threshold': 3.5
	}
}

# Default preset to use
DEFAULT_PRESET = 'medium'

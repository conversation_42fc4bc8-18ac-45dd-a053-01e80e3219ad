"""
ODrive Motor Performance Plotting Script with Smoothing

File: plot_motor_performance.py
Purpose: Plots motor rotations per second vs. current draw from ODrive CSV data with noise reduction
Safety Level: Non-Critical
Author: Brian <PERSON>
Date: 2025-07-08
"""

import os
import csv
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.ndimage import uniform_filter1d


# Import smoothing configuration
try:
	from smoothing_config import (
		SMOOTHING_WINDOW_SIZE, DECIMATION_FACTOR, SAVGOL_WINDOW_LENGTH,
		SAVGOL_POLY_ORDER, OUTLIER_THRESHOLD, MIN_DATA_POINTS,
		FIGURE_SIZE, SAVE_DPI, SMOOTHED_LINEWIDTH, SMOOTHED_MARKERSIZE,
		SMOOTHED_ALPHA, RAW_LINEWIDTH, RAW_MARKERSIZE, RAW_ALPHA,
		GRID_ALPHA, GRID_LINESTYLE, GRID_LINEWIDTH
	)
except ImportError:
	# Fallback values if the config file is missing
	SMOOTHING_WINDOW_SIZE = 50
	DECIMATION_FACTOR = 5
	SAVGOL_WINDOW_LENGTH = 51
	SAVGOL_POLY_ORDER = 3
	OUTLIER_THRESHOLD = 3.0
	MIN_DATA_POINTS = 100
	FIGURE_SIZE = (14, 10)
	SAVE_DPI = 300
	SMOOTHED_LINEWIDTH = 2.5
	SMOOTHED_MARKERSIZE = 6
	SMOOTHED_ALPHA = 0.8
	RAW_LINEWIDTH = 1
	RAW_MARKERSIZE = 2
	RAW_ALPHA = 0.6
	GRID_ALPHA = 0.4
	GRID_LINESTYLE = '--'
	GRID_LINEWIDTH = 0.5


def remove_outliers(data: list[float], threshold: float = OUTLIER_THRESHOLD) -> list[float]:
	"""
	Removes outliers from data using standard deviation method.

	Args:
		data: List of numerical values
		threshold: Number of standard deviations for outlier detection

	Returns:
		list: Data with outliers removed
	"""
	if len(data) < 10:
		return data
	
	data_array = np.array(data)
	mean_val = np.mean(data_array)
	std_val = np.std(data_array)
	
	if std_val == 0:
		return data
	
	# Keep values within threshold standard deviations
	mask = np.abs(data_array - mean_val) <= threshold * std_val
	return data_array[mask].tolist()


def apply_moving_average(data: list[float], window_size: int = SMOOTHING_WINDOW_SIZE) -> list[float]:
	"""
	Applies moving average smoothing to data.

	Args:
		data: List of numerical values
		window_size: Size of the moving average window

	Returns:
		list: Smoothed data
	"""
	if len(data) < window_size:
		return data
	
	data_array = np.array(data)
	smoothed = uniform_filter1d(data_array, size=window_size, mode='nearest')
	return smoothed.tolist()


def apply_savgol_filter(data: list[float], window_length: int = SAVGOL_WINDOW_LENGTH,
						poly_order: int = SAVGOL_POLY_ORDER) -> list[float]:
	"""
	Applies Savitzky-Golay filter for smoothing while preserving peaks.

	Args:
		data: List of numerical values
		window_length: Length of the filter window (must be odd)
		poly_order: Order of the polynomial used for fitting

	Returns:
		list: Smoothed data
	"""
	if len(data) < window_length:
		return data
	
	# Ensure window length is odd and valid
	if window_length % 2 == 0:
		window_length += 1
	
	if window_length > len(data):
		window_length = len(data) if len(data) % 2 == 1 else len(data) - 1
	
	if poly_order >= window_length:
		poly_order = window_length - 1
	
	try:
		data_array = np.array(data)
		smoothed = signal.savgol_filter(data_array, window_length, poly_order)
		return smoothed.tolist()
	except ValueError:
		return data


def decimate_data(rps_data: list[float], current_data: list[float],
				  factor: int = DECIMATION_FACTOR) -> tuple[list[float], list[float]]:
	"""
	Reduces data density by taking every nth point.

	Args:
		rps_data: List of RPS values
		current_data: List of current values
		factor: Decimation factor (take every nth point)

	Returns:
		tuple: Decimated RPS and current data
	"""
	if factor <= 1 or len(rps_data) != len(current_data):
		return rps_data, current_data
	
	decimated_rps = rps_data[::factor]
	decimated_current = current_data[::factor]
	
	return decimated_rps, decimated_current


def validate_csv_file(file_path: str) -> bool:
	"""
	Validates that a CSV file contains required columns for motor data analysis.
	
	Args:
		file_path: Absolute path to the CSV file
		
	Returns:
		bool: True if the file contains required columns, False otherwise
	"""
	required_columns = ['ODrive 1 Velocity', 'ODrive 1 Current']
	
	try:
		with open(file_path, 'r') as csvfile:
			reader = csv.DictReader(csvfile)
			fieldnames = reader.fieldnames
			
			if fieldnames is None:
				return False
			
			for column in required_columns:
				if column not in fieldnames:
					return False
		
		return True
	
	except (IOError, OSError):
		return False


def read_motor_data(file_path: str, apply_smoothing: bool = True) -> tuple[list[float], list[float]]:
	"""
	Reads motor velocity and current data from CSV file with optional smoothing.

	Args:
		file_path: Absolute path to the CSV file
		apply_smoothing: Whether to apply smoothing filters to the data

	Returns:
		tuple: Lists of RPS values and current values (smoothed if requested)
	"""
	rps_values = []
	current_values = []
	
	with open(file_path, 'r') as csvfile:
		reader = csv.DictReader(csvfile)
		for row in reader:
			try:
				rps_value = abs(float(row['ODrive 1 Velocity']))
				current_value = float(row['ODrive 1 Current'])
				rps_values.append(rps_value)
				current_values.append(current_value)
			except (ValueError, KeyError):
				continue
	
	if not apply_smoothing or len(rps_values) < MIN_DATA_POINTS:
		return rps_values, current_values
	
	# Apply a smoothing pipeline
	print(f"  Original data points: {len(rps_values)}")
	
	# Step 1: Remove outliers from current measurements (RPS usually doesn't have outliers)
	current_values = remove_outliers(current_values)
	
	# Ensure both arrays have the same length after outlier removal
	min_length = min(len(rps_values), len(current_values))
	rps_values = rps_values[:min_length]
	current_values = current_values[:min_length]
	
	print(f"  After outlier removal: {len(current_values)}")
	
	# Step 2: Apply Savitzky-Golay filter for better peak preservation
	current_values = apply_savgol_filter(current_values)
	rps_values = apply_savgol_filter(rps_values)
	
	# Step 3: Apply moving average for additional smoothing
	current_values = apply_moving_average(current_values)
	rps_values = apply_moving_average(rps_values)
	
	# Step 4: Decimate data to reduce point density
	rps_values, current_values = decimate_data(rps_values, current_values)
	
	print(f"  Final smoothed points: {len(rps_values)}")
	
	return rps_values, current_values


def get_csv_files(input_folder: str) -> list[str]:
	"""
	Gets all CSV files from the input folder.
	
	Args:
		input_folder: Path to the input folder
		
	Returns:
		list: List of CSV file paths
	"""
	csv_files = []
	
	if not os.path.exists(input_folder):
		return csv_files
	
	for filename in os.listdir(input_folder):
		if filename.endswith('.csv'):
			file_path = os.path.join(input_folder, filename)
			if validate_csv_file(file_path):
				csv_files.append(file_path)
	
	return csv_files


def rps_to_rpm(rps_value: float) -> float:
	"""
	Converts rotations per second to rotations per minute.
	
	Args:
		rps_value: Rotations per second value
		
	Returns:
		float: Rotations per minute value
	"""
	return rps_value * 60.0


def rpm_to_rps(rpm_value: float) -> float:
	"""
	Converts rotations per minute to rotations per second.
	
	Args:
		rpm_value: Rotations per minute value
		
	Returns:
		float: Rotations per second value
	"""
	return rpm_value / 60.0


def create_comparison_plot(input_folder: str) -> None:
	"""
	Creates a side-by-side comparison plot showing both raw and smoothed data.

	Args:
		input_folder: Path to the input folder containing CSV files
	"""
	csv_files = get_csv_files(input_folder)
	
	if not csv_files:
		print(f"No valid CSV files found in {input_folder}")
		return
	
	print(f"Creating comparison plot with {len(csv_files)} CSV files...")
	
	# Create a figure with two subplots side by side
	fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
	
	plt.rcParams['keymap.zoom'] = ['ctrl+plus', 'ctrl+equal']
	plt.rcParams['keymap.back'] = ['ctrl+minus', 'ctrl+underscore']
	plt.rcParams['keymap.pan'] = ['p']
	plt.rcParams['keymap.home'] = ['h', 'r', 'home']
	
	colormap = plt.get_cmap('tab10')
	colors = [colormap(i) for i in range(len(csv_files))]
	
	# Process each file and plot on both subplots
	for i, file_path in enumerate(csv_files):
		filename = os.path.basename(file_path)
		test_name = os.path.splitext(filename)[0]
		
		print(f"Processing {filename}...")
		
		# Get raw data
		rps_raw, current_raw = read_motor_data(file_path, apply_smoothing=False)
		# Get smoothed data
		rps_smooth, current_smooth = read_motor_data(file_path, apply_smoothing=True)
		
		if not rps_raw or not current_raw:
			print(f"Warning: No valid data found in {filename}")
			continue
		
		# Use different markers and line styles for better distinction
		marker_styles = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']
		line_styles = ['-', '--', '-.', ':']
		
		marker = marker_styles[i % len(marker_styles)]
		linestyle = line_styles[i % len(line_styles)]
		
		# Plot raw data on left subplot
		ax1.plot(rps_raw, current_raw,
				 color=colors[i],
				 marker=marker,
				 linestyle=linestyle,
				 label=test_name,
				 markersize=RAW_MARKERSIZE,
				 linewidth=RAW_LINEWIDTH,
				 alpha=RAW_ALPHA,
				 markevery=max(1, len(rps_raw) // 100))
		
		# Plot smoothed data on right subplot
		ax2.plot(rps_smooth, current_smooth,
				 color=colors[i],
				 marker=marker,
				 linestyle=linestyle,
				 label=test_name,
				 markersize=SMOOTHED_MARKERSIZE,
				 linewidth=SMOOTHED_LINEWIDTH,
				 alpha=SMOOTHED_ALPHA,
				 markevery=max(1, len(rps_smooth) // 50))
	
	# Configure left subplot (Raw Data)
	ax1.set_xlabel("Mechanical Frequency (Hz)", fontsize=12, fontweight='bold')
	ax1.set_ylabel("Current Draw (A)", fontsize=12, fontweight='bold')
	ax1.set_title("Raw Data", fontsize=14, fontweight='bold', pad=20)
	ax1.grid(True, alpha=GRID_ALPHA, linestyle=GRID_LINESTYLE, linewidth=GRID_LINEWIDTH)
	ax1.set_axisbelow(True)
	
	# Configure right subplot (Smoothed Data)
	ax2.set_xlabel("Mechanical Frequency (Hz)", fontsize=12, fontweight='bold')
	ax2.set_ylabel("Current Draw (A)", fontsize=12, fontweight='bold')
	ax2.set_title("Smoothed Data", fontsize=14, fontweight='bold', pad=20)
	ax2.grid(True, alpha=GRID_ALPHA, linestyle=GRID_LINESTYLE, linewidth=GRID_LINEWIDTH)
	ax2.set_axisbelow(True)
	
	# Add secondary x-axis for RPM on both subplots
	secax1 = ax1.secondary_xaxis('top', functions=(rps_to_rpm, rpm_to_rps))
	secax1.set_xlabel('RPM', fontsize=11, fontweight='bold')
	
	secax2 = ax2.secondary_xaxis('top', functions=(rps_to_rpm, rpm_to_rps))
	secax2.set_xlabel('RPM', fontsize=11, fontweight='bold')
	
	# Add shared legend
	handles, labels = ax1.get_legend_handles_labels()
	fig.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, 0.95),
			   ncol=len(csv_files), frameon=True, fancybox=True, shadow=True)
	
	# Add main title
	fig.suptitle("Motor Current Draw vs Absolute Rotational Speed - Comparison",
				 fontsize=16, fontweight='bold', y=0.98)
	
	# Add smoothing info
	smoothing_info = (f"Smoothing: MA={SMOOTHING_WINDOW_SIZE}, "
					  f"SavGol={SAVGOL_WINDOW_LENGTH}, "
					  f"Decim={DECIMATION_FACTOR}x")
	fig.text(0.02, 0.02, smoothing_info, fontsize=8, alpha=0.7,
			 transform=fig.transFigure, style='italic')
	
	# Keyboard shortcuts info
	fig.text(0.98, 0.02, 'Keyboard shortcuts: Ctrl+Plus (zoom), Ctrl+Minus (zoom out), P (pan), H (home)',
			 fontsize=8, alpha=0.7, transform=fig.transFigure, ha='right')
	
	plt.tight_layout()
	plt.subplots_adjust(top=0.85)  # Make room for the legend and title
	
	# Save the comparison plot
	script_dir = os.path.dirname(os.path.abspath(__file__))
	output_dir = os.path.join(script_dir, "output_data")
	
	if not os.path.exists(output_dir):
		os.makedirs(output_dir)
	
	output_path = os.path.join(output_dir, "motor_performance_comparison.png")
	plt.savefig(output_path, dpi=SAVE_DPI, bbox_inches='tight', facecolor='white', edgecolor='none')
	
	plt.show()
	
	print(f"Comparison plot saved to: {output_path}")
	return


def create_motor_performance_plot(input_folder: str, enable_smoothing: bool = True) -> None:
	"""
	Creates a combined plot of motor performance data from all CSV files.

	Args:
		input_folder: Path to the input folder containing CSV files
		enable_smoothing: Whether to apply smoothing to the data
	"""
	csv_files = get_csv_files(input_folder)
	
	if not csv_files:
		print(f"No valid CSV files found in {input_folder}")
		return
	
	print(f"Processing {len(csv_files)} CSV files with smoothing {'enabled' if enable_smoothing else 'disabled'}...")
	
	fig, ax1 = plt.subplots(figsize=FIGURE_SIZE)
	
	plt.rcParams['keymap.zoom'] = ['ctrl+plus', 'ctrl+equal']
	plt.rcParams['keymap.back'] = ['ctrl+minus', 'ctrl+underscore']
	plt.rcParams['keymap.pan'] = ['p']
	plt.rcParams['keymap.home'] = ['h', 'r', 'home']
	
	colormap = plt.get_cmap('tab10')
	colors = [colormap(i) for i in range(len(csv_files))]
	
	for i, file_path in enumerate(csv_files):
		filename = os.path.basename(file_path)
		test_name = os.path.splitext(filename)[0]
		
		print(f"Processing {filename}...")
		rps_values, current_values = read_motor_data(file_path, apply_smoothing=enable_smoothing)
		
		if not rps_values or not current_values:
			print(f"Warning: No valid data found in {filename}")
			continue
		
		# Use different markers and line styles for better distinction
		marker_styles = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']
		line_styles = ['-', '--', '-.', ':']
		
		marker = marker_styles[i % len(marker_styles)]
		linestyle = line_styles[i % len(line_styles)]
		
		# Adjust marker size and line width based on smoothing
		if enable_smoothing:
			markersize = SMOOTHED_MARKERSIZE
			linewidth = SMOOTHED_LINEWIDTH
			alpha = SMOOTHED_ALPHA
		else:
			markersize = RAW_MARKERSIZE
			linewidth = RAW_LINEWIDTH
			alpha = RAW_ALPHA
		
		ax1.plot(rps_values, current_values,
				 color=colors[i],
				 marker=marker,
				 linestyle=linestyle,
				 label=test_name,
				 markersize=markersize,
				 linewidth=linewidth,
				 alpha=alpha,
				 markevery=max(1, len(rps_values) // 50))  # Show fewer markers for cleaner look
	
	# Enhanced styling and labels
	ax1.set_xlabel("Mechanical Frequency (Hz)", fontsize=12, fontweight='bold')
	ax1.set_ylabel("Current Draw (A)", fontsize=12, fontweight='bold')
	
	title_suffix = " (Smoothed)" if enable_smoothing else " (Raw Data)"
	ax1.set_title("Motor Current Draw vs Absolute Rotational Speed" + title_suffix,
				  fontsize=14, fontweight='bold', pad=20)
	
	# Enhanced grid
	ax1.grid(True, alpha=GRID_ALPHA, linestyle=GRID_LINESTYLE, linewidth=GRID_LINEWIDTH)
	ax1.set_axisbelow(True)
	
	# Secondary axis for RPM
	secax_x = ax1.secondary_xaxis('top', functions=(rps_to_rpm, rpm_to_rps))
	secax_x.set_xlabel('Absolute Rotations Per Minute (RPM)', fontsize=11, fontweight='bold')
	
	# Improved legend
	legend = ax1.legend(loc='upper left', bbox_to_anchor=(1.02, 1),
						frameon=True, fancybox=True, shadow=True)
	legend.get_frame().set_facecolor('white')
	legend.get_frame().set_alpha(0.9)
	
	# Add smoothing info text
	if enable_smoothing:
		smoothing_info = (f"Smoothing: MA={SMOOTHING_WINDOW_SIZE}, "
						  f"SavGol={SAVGOL_WINDOW_LENGTH}, "
						  f"Decim={DECIMATION_FACTOR}x")
		fig.text(0.02, 0.95, smoothing_info, fontsize=8, alpha=0.7,
				 transform=fig.transFigure, style='italic')
	
	# Keyboard shortcuts info
	fig.text(0.02, 0.02, 'Keyboard shortcuts: Ctrl+Plus (zoom in), Ctrl+Minus (zoom out), P (pan), H (home)',
			 fontsize=8, alpha=0.7, transform=fig.transFigure)
	
	plt.tight_layout()
	
	# Save the plot
	script_dir = os.path.dirname(os.path.abspath(__file__))
	output_dir = os.path.join(script_dir, "output_data")
	
	if not os.path.exists(output_dir):
		os.makedirs(output_dir)
	
	# Save with the appropriate filename
	filename_suffix = "_smoothed" if enable_smoothing else "_raw"
	output_path = os.path.join(output_dir, f"motor_performance_plot{filename_suffix}.png")
	plt.savefig(output_path, dpi=SAVE_DPI, bbox_inches='tight', facecolor='white', edgecolor='none')
	
	plt.show()
	
	print(f"Plot saved to: {output_path}")
	return


def main() -> None:
	"""
	Main function to execute the motor performance plotting script.
	Creates a side-by-side comparison plot and individual plots.
	"""
	script_dir = os.path.dirname(os.path.abspath(__file__))
	input_folder = os.path.join(script_dir, "input_data")

	if not os.path.exists(input_folder):
		print(f"Input folder does not exist: {input_folder}")
		print("Please create the input folder and add CSV files.")
		return

	print("=" * 70)
	print("ODrive Motor Performance Analysis with Advanced Smoothing")
	print("=" * 70)

	# Create side-by-side comparison plot (recommended)
	print("\n1. Creating SIDE-BY-SIDE COMPARISON plot (recommended)...")
	create_comparison_plot(input_folder)

	# Ask user if they want individual plots too
	print("\n" + "=" * 70)
	print("Comparison plot complete!")
	print("\nWould you like individual plots as well? (y/n): ", end="")

	try:
		response = input().lower().strip()
		if response in ['y', 'yes']:
			print("\n2. Creating individual SMOOTHED plot...")
			create_motor_performance_plot(input_folder, enable_smoothing=True)

			print("\n3. Creating individual RAW data plot...")
			create_motor_performance_plot(input_folder, enable_smoothing=False)

			print("\n" + "=" * 70)
			print("All plots complete! Check the output_data folder for:")
			print("  - motor_performance_comparison.png (side-by-side comparison)")
			print("  - motor_performance_plot_smoothed.png (individual smoothed)")
			print("  - motor_performance_plot_raw.png (individual raw)")
		else:
			print("\n" + "=" * 70)
			print("Analysis complete! Check the output_data folder for:")
			print("  - motor_performance_comparison.png (side-by-side comparison)")
	except (KeyboardInterrupt, EOFError):
		print("\n\nAnalysis complete! Check the output_data folder for:")
		print("  - motor_performance_comparison.png (side-by-side comparison)")

	print("=" * 70)
	return


if __name__ == "__main__":
	main()
